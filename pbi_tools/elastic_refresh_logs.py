from datetime import datetime, timedelta, timezone
from typing import Iterable

import httpx
import rich
from pydantic import BaseModel, computed_field

from pbi_tools.connectors import elastic


class ElasticRefreshLog(BaseModel):
    shard_id: str
    message: str
    job_guid: str
    timestamp: str

    @computed_field
    def is_full(self) -> bool:
        if self.message.endswith("full"):
            return True
        if self.message.endswith("incremental"):
            return False
        raise ValueError(f"Unexpected message: {self.message}")


async def get_elastic_refresh_logs(shard_ids: Iterable[str]):
    try:
        for shard_id in shard_ids:
            response = await elastic.elastic_search(
                "logs-kubernetes.container_logs.workloads-prod",
                datetime.now(tz=timezone.utc) - timedelta(days=14),
                ["message", "data_job*", "@timestamp"],
                [
                    {"term": {"data_job.job_name.keyword": {"value": "pbi-refresh"}}},
                    {
                        "term": {
                            "data_job.target.shard_id.keyword": {
                                "value": shard_id.lower()
                            }
                        }
                    },
                    {"match_phrase": {"message": "Triggering refresh for shard"}},
                ],
            )

            for hit in response["hits"]["hits"]:
                yield ElasticRefreshLog(
                    shard_id=shard_id,
                    message=hit["fields"]["message"][0],
                    job_guid=hit["fields"]["data_job.active_job_guid"][0],
                    timestamp=hit["fields"]["@timestamp"][0],
                )

    except httpx.HTTPStatusError as e:
        rich.print(e.response.status_code)
        rich.print(e.response.content)
        raise e


async def _test():
    async for log in get_elastic_refresh_logs(["0599cc9f-ade6-48d7-a8e7-dcbae0651b39"]):
        rich.print(log)


if __name__ == "__main__":
    import asyncio

    asyncio.run(_test())
