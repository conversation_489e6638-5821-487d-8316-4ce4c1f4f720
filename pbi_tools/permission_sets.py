import json
from typing import Iterable

import httpx
from pydantic import BaseModel
from tqdm import tqdm

from pbi_tools import elastic_refresh_logs, fabric_capacity_metrics, refresh_history
from pbi_tools.cache import cache
from pbi_tools.connectors import dataset_manager, pipeline_manager


class PermissionSetSummary(BaseModel):
    uuid: str
    costs: fabric_capacity_metrics.FabricMetricsPermissionSetCosts
    viewers: list[int]
    contributors: list[int]
    permission_set_json: str
    refreshes: list[refresh_history.Refresh]


async def get_shard_contributors(shard_ids: Iterable[str]):
    studio_ids: set[int] = set()

    for shard_id in shard_ids:
        try:
            permission_set = await dataset_manager.get_permission_set(shard_id)
            for permission_set_entry in permission_set:
                studio_ids.add(permission_set_entry["studio_id"])
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                print(f"  No permission set found for shard {shard_id}")
            else:
                raise e

    return list(studio_ids)


async def get_permission_set_json(shard_ids: Iterable[str]):
    for shard_id in shard_ids:
        try:
            permission_set = await dataset_manager.get_permission_set(shard_id)

            return json.dumps(permission_set)

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                print(f"  No permission set found for shard {shard_id}")
            else:
                raise e

    return ""


async def get_shard_viewers(shard_ids: Iterable[str]):
    studio_ids: set[int] = set()

    for shard_id in shard_ids:
        try:
            profiles = await dataset_manager.get_shard_profiles(shard_id)
            for profile in profiles:
                studio_ids.add(int(profile["studio_id"]))
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                print(f"  No profiles found for shard {shard_id}")
            else:
                raise e

    return list(studio_ids)


@cache
async def download_permission_set_data():
    operation_costs = fabric_capacity_metrics.get_operation_costs()

    costs_by_permission_set = (
        await fabric_capacity_metrics.summarize_operation_costs_by_permission_set(
            operation_costs
        )
    )

    refreshes = await refresh_history.download_refresh_history()

    permission_set_summaries: list[PermissionSetSummary] = []

    for permission_set_id, permission_set_cost in (
        progress := tqdm(costs_by_permission_set)
    ):
        progress.set_description(f"Processing permission set {permission_set_id}")
        summary = PermissionSetSummary(
            uuid=permission_set_id,
            costs=permission_set_cost,
            viewers=await get_shard_viewers(permission_set_cost.shards),
            contributors=await get_shard_contributors(permission_set_cost.shards),
            permission_set_json=await get_permission_set_json(
                permission_set_cost.shards
            ),
            refreshes=[
                r for r in refreshes if r.workspace_id in permission_set_cost.shards
            ],
        )

        permission_set_summaries.append(summary)

    return permission_set_summaries
