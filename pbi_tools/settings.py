from pydantic_settings import BaseSettings

class Settings(BaseSettings):

    powerbi_capacity_metrics_workspace_id: str
    powerbi_capacity_metrics_dataset_id: str

    dataset_manager_url: str
    dataset_manager_key: str

    user_service_url: str
    user_service_key: str

    pipeline_manager_url: str
    pipeline_manager_key: str

    default_connection_timeout: int = 10

    elastic_api_key: str
    elastic_url: str


_settings: Settings | None = None


def settings():
    # having settings being lazy loaded makes it easier to swap
    # out the settings object for testing.
    # Instantiating Settings should fail if some of the required
    # vars are not defined, but we want tests to be able to inject their own settings.
    global _settings
    if _settings is None:
        _settings = Settings(_env_file=".env")  # type: ignore
    return _settings
