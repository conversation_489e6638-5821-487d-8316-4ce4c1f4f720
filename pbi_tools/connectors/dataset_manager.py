import base64
import json
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Literal

import httpx
from pbi_tools.connectors.common import get_model
from pbi_tools.settings import settings
from pydantic import BaseModel


class ReleaseMode(str, Enum):
    DEV_RECREATE = "dev_recreate"
    FORCE_RECREATE = "force_recreate"


ShardVersion = str
DatasetId = str
ShardWorkspaceId = str
PermissionSetUUID = uuid.UUID


class DashboardRelease(BaseModel):
    user_ids: list[int]
    version_id: str
    release_mode: str


class Shard(BaseModel):
    version: ShardVersion
    dataset_id: DatasetId
    dataset_name: str | None
    workspace_id: ShardWorkspaceId
    workspace_name: str
    capacity_id: str
    creation_timestamp: datetime
    permission_set_uuid: PermissionSetUUID
    last_refresh_timestamp: datetime | None


class Capacity(BaseModel):
    id: str
    name: str
    tier: str
    state: Literal["Active", "Paused", "Unknown"]
    is_default: bool
    is_default_for_releases: bool


_client = httpx.AsyncClient(
    base_url=settings().dataset_manager_url,
    headers={"x-api-key": settings().dataset_manager_key},
    timeout=settings().default_connection_timeout,
)


async def _get_any_json(path: str, params: dict[str, Any] | None = None):
    response = await _client.get(path, params=params)
    response.raise_for_status()
    return response.json()


async def _put(path: str, json: Any = None):
    response = await _client.put(path, json=json)
    response.raise_for_status()


async def _post(path: str, json: Any = None):
    response = await _client.post(path, json=json)
    response.raise_for_status()
    return response.json()


async def release_version_for_all(dashboard_release: DashboardRelease):
    await _put(
        f"/version/{dashboard_release.version_id}/release_for/all/{dashboard_release.release_mode}"
    )


async def release_version_for_studios(dashboard_release: DashboardRelease):
    await _put(
        f"/version/{dashboard_release.version_id}/release_for/{dashboard_release.release_mode}",
        json=dashboard_release.user_ids,
    )


async def set_default_version(version_id: str):
    await _put(f"/version/{version_id}/set_default")


async def get_all_versions():
    return await _get_any_json("/version/all")


async def get_active_shards_for_studios(studio_ids: list[int]):
    return await get_model(
        _client,
        list[dict[str, Any]],
        "/shard/active",
        params={"studio_ids": studio_ids},
    )


async def get_active_shard(studio_id: int | str) -> Shard:
    return await get_model(_client, Shard, f"/studio/{studio_id}/shard/active")

async def get_shard(shard_id: str) -> Shard:
    return await get_model(_client, Shard, f"/shard/{shard_id}")



async def get_shard_profiles(shard_id: str) -> list[dict[str, Any]]:
    return await get_model(_client, list[dict[str, Any]], f"/shard/{shard_id}/profiles")


async def dax_query(studio_id: int | str, query: str):
    base64_query = base64.b64encode(query.encode("utf-8")).decode("utf-8")
    return await _post(f"studio/{studio_id}/query", json={"base64_query": base64_query})


async def dax_query_shard(shard_id: str, query: str):
    base64_query = base64.b64encode(query.encode("utf-8")).decode("utf-8")
    return await _post(f"shard/{shard_id}/query", json={"base64_query": base64_query})


async def get_capacities():
    return await get_model(_client, list[Capacity], "/capacity")


async def add_debug_admins(workspace_id: str):
    return await _put(f"/shard/{workspace_id}/add-debug-admins")


async def get_permission_set(shard_id: str):

    return await get_model(_client, list[dict[str, Any]], f"/shard/{shard_id}/permission-set")

    # result = await dax_query_shard(shard_id, "EVALUATE permission_set")
    # return json.loads(result[0]["permission_set[permission_set]"])
