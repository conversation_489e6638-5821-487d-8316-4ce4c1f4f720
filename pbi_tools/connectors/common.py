from typing import Any, Type

import httpx
from pydantic import BaseModel, TypeAdapter


class PaginatedResponse[T](BaseModel):
    data: list[T]
    count: int


async def get_model[T](
    client: httpx.AsyncClient,
    model: Type[T],
    path: str,
    params: dict[str, Any] | None = None,
) -> T:
    response = await client.get(path, params=params)
    response.raise_for_status()
    return TypeAdapter(model).validate_json(response.content)
