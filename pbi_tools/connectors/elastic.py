
from datetime import datetime
import httpx

from pbi_tools.settings import settings


_client = httpx.AsyncClient(timeout=60, base_url=settings().elastic_url, headers={"Authorization": f"ApiKey {settings().elastic_api_key}", "Content-Type": "application/json"})


async def elastic_search(index: str, date_from: datetime, fields: list[str], filters: list[dict]):
    url = f"/{index}/_search"

    data = {
        "sort": [{"@timestamp": {"order": "desc"}}],
        "fields": [{"field": field} for field in fields],
        "_source": False,
        "size": 500,
        "query": {
            "bool": {
                "must": [],
                "filter": filters + [
                    {
                        "range": {
                            "@timestamp": {
                                "format": "strict_date_optional_time",
                                "gte": date_from.isoformat(),
                            }
                        }
                    },
                ],
                "should": [],
                "must_not": [],
            }
        },
    }


    response = await _client.post(url, json=data)
    response.raise_for_status()
    return response.json()


