from functools import lru_cache

import httpx
from pydantic import BaseModel

from pbi_tools.connectors.common import PaginatedResponse, get_model
from pbi_tools.settings import settings

_client = httpx.AsyncClient(
    base_url=settings().user_service_url,
    headers={"x-api-key": settings().user_service_key},
    timeout=settings().default_connection_timeout,
)


class User(BaseModel):
    email: str
    first_name: str
    last_name: str
    company_name: str
    id: str
    legacy_id: int


class Organization(BaseModel):
    name: str
    id: str
    legacy_id: int


@lru_cache(maxsize=10000)
async def search_users(search_substring: str, offset: int, limit: int):
    params = {"search_substring": search_substring, "offset": offset, "limit": limit}
    results_page = await get_model(
        _client, PaginatedResponse[User], "/user/search", params
    )
    return results_page.data


@lru_cache(maxsize=10000)
async def get_user(user_id: str) -> User:
    return await get_model(_client, User, f"/user/{user_id}")


@lru_cache(maxsize=10000)
async def get_user_by_studio_id(studio_id: int) -> User | None:
    try:
        return await get_model(_client, User, f"/user/legacy/{studio_id}")
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            return None


@lru_cache(maxsize=10000)
async def get_org_by_studio_id(studio_id: int) -> Organization | None:
    try:
        return await get_model(
            _client, Organization, f"/organization/legacy/{studio_id}"
        )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            return None
