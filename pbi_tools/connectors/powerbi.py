import asyncio
from calendar import c
from azure.identity.aio import DefaultAzureCredential as AioDefaultAzureCredential
import httpx

credential = AioDefaultAzureCredential(logging_enable=True)


_client = httpx.AsyncClient(timeout=60)


async def _get_token():
    #token = await credential.get_token("https://analysis.windows.net/powerbi/api")
    token = await credential.get_token("https://analysis.windows.net/powerbi/api/.default")
    return token.token

MAX_RETRIES = 10

async def _get_with_retries(url: str):
    attempts = 0
    while attempts < MAX_RETRIES:
        response = await _client.get(
            url,
            headers={"Authorization": "Bearer " + await _get_token()},
        )
        if response.status_code == 429:
            retry_after = int(response.headers.get("Retry-After", 1))
            print(f"Rate limited, waiting for {retry_after} seconds")
            await asyncio.sleep(retry_after)
            attempts += 1
            continue
        response.raise_for_status()
        return response.json()
    raise Exception("Too many retries")


async def execute_dax(workspace_id: str, dataset_id: str, dax_query: str):

    
    url =  f"https://api.powerbi.com/v1.0/myorg/groups/{workspace_id}/datasets/{dataset_id}/executeQueries"


    token = await _get_token()
    json =  {
                "queries": [{"query": dax_query}],
                "serializerSettings": {"includeNulls": True},
            }
    
    headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }
    
    response = await _client.post(url, json=json, headers=headers)
    response.raise_for_status()
    
    return response.json()["results"][0]["tables"][0]["rows"]


def dax_result_to_table(result_rows):
    known_columns = []
    for row in result_rows:
        for column_name in row.keys():
            if column_name not in known_columns:
                known_columns.append(column_name)


    rows = []
    for row in result_rows:
        row_values = []
        for column_name in known_columns:
            row_values.append(row.get(column_name))
        rows.append(row_values)
    return known_columns, rows
    


async def get_refresh_history(group_id: str, dataset_id: str):

    response = await _get_with_retries(f"https://api.powerbi.com/v1.0/myorg/groups/{group_id}/datasets/{dataset_id}/refreshes")

    return response["value"]


async def get_refresh_details(group_id: str, dataset_id: str, refresh_id: str):
    return await _get_with_retries(
        f"https://api.powerbi.com/v1.0/myorg/groups/{group_id}/datasets/{dataset_id}/refreshes/{refresh_id}",
    )