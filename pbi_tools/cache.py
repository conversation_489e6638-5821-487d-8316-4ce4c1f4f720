from pathlib import Path
from typing import Awaitable, Callable

from pydantic import TypeAdapter


class Cache:
    def __init__(self) -> None:
        self.use_cache = False

    def __call__[T](self, f: Callable[[], Awaitable[list[T]]]):
        async def wrapper(t: type[T]) -> list[T]:
            if self.use_cache:
                return self._load_cache(t, f.__name__)
            result = await f()
            self._save_cache(result, f.__name__)
            return result

        return wrapper

    def _load_cache[T](self, _type: type[T], name: str) -> list[T]:
        result: list[T] = []
        cache_file = Path(f"cache/{name}.jsons")

        with cache_file.open("r") as f:
            for line in f:
                result.append(TypeAdapter(_type).validate_json(line))
        return result

    def _save_cache[T](self, data: list[T], name: str) -> None:
        cache_file = Path(f"cache/{name}.jsons")
        cache_file.parent.mkdir(parents=True, exist_ok=True)

        with cache_file.open("wb") as f:
            for item in data:
                f.write(TypeAdapter(type(item)).dump_json(item))
                f.write(b"\n")


cache = Cache()
