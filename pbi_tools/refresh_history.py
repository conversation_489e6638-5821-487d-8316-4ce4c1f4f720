import sys

from pydantic import BaseModel
from tqdm import tqdm

from pbi_tools import elastic_refresh_logs, fabric_capacity_metrics
from pbi_tools.connectors import dataset_manager, pipeline_manager


class Refresh(BaseModel):
    fabric_metrics: fabric_capacity_metrics.FabricMetrics
    elastic_refresh_log: list[elastic_refresh_logs.ElasticRefreshLog]
    trigger_reasons: list[pipeline_manager.PipelineGetReasons] | None = None
    workspace_id: str


async def get_refresh_history(capacity_id: str) -> list[Refresh]:
    item_id_to_workspace_id = await fabric_capacity_metrics.query_item_id_workspace_id(
        capacity_id
    )

    fabric_metrics: fabric_capacity_metrics.FabricMetrics = (
        await fabric_capacity_metrics.query_fabric_capacity_metrics_v2(
            capacity_id=capacity_id
        )
    )

    merged_refreshes: list[Refresh] = []

    for _, workspace_id in item_id_to_workspace_id.items():
        refresh_logs: list[elastic_refresh_logs.ElasticRefreshLog] = [
            log
            async for log in elastic_refresh_logs.get_elastic_refresh_logs(
                [workspace_id]
            )
        ]

        merged_refreshes.append(
            Refresh(
                fabric_metrics=fabric_metrics.for_shard_id(workspace_id),
                elastic_refresh_log=refresh_logs,
                workspace_id=workspace_id,
            )
        )

    return merged_refreshes


async def download_refresh_history():
    refreshes: list[Refresh] = []
    capacities = await dataset_manager.get_capacities()

    for capacity in capacities:
        if capacity.state != "Active":
            continue
        sys.stderr.write(
            f"Getting refresh history for capacity {capacity.name} ({capacity.id})\n"
        )
        capacity_refreshes = await get_refresh_history(capacity.id)

        refreshes.extend(capacity_refreshes)

    for refresh in tqdm(
        refreshes, "Getting refresh trigger reasons from Pipeline Manager"
    ):
        refresh.trigger_reasons = (
            await pipeline_manager.trigger_reasons(refresh.elastic_refresh_log.job_guid)
        ).data

    return refreshes
