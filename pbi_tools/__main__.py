import json

import fire
from rich import print, text
from rich.console import Console
from rich.table import Table

from pbi_tools import orgs, permission_sets, refresh_history
from pbi_tools.cache import cache
from pbi_tools.connectors import user_service

console = Console(force_terminal=True)

print = console.print


TOTAL_COST_EURO = 3040


async def permission_set_human_description(permission_set_json: str):
    permission_set = json.loads(permission_set_json)

    text = ""
    for entry in permission_set:
        product = entry.get("product_name")
        studio_id = entry["studio_id"]

        org = await user_service.get_org_by_studio_id(int(studio_id))
        user = await user_service.get_user_by_studio_id(int(studio_id))

        if product is None:
            product = "Everything"

        if org:
            text += f"\t{product} from {org.legacy_id} ({org.name})\n"
        elif user:
            text += f"\t{product} from {user.legacy_id} ({user.first_name} {user.last_name} {user.company_name})\n"
        else:
            text += f"\t{product} from {studio_id} (unknown user)\n"

    return text


async def prepare_report(orgs: dict[int, orgs.OrganizationSummary]):
    orgs_by_cost = sorted(
        orgs.values(),
        key=lambda x: sum(ps.cost for ps in x.permission_sets),
        reverse=True,
    )

    total_cost = sum(
        ps.permission_set.costs.cost
        for org in orgs_by_cost
        for ps in org.permission_sets
    )

    cost_per_cu = TOTAL_COST_EURO / total_cost

    for org in orgs_by_cost:
        table = Table(title=org.name())

        table.add_column("Permission Set")
        table.add_column("Refreshes")
        table.add_column("Fulls")
        table.add_column("Org Cost [CUs]")
        table.add_column("Total Cost [CUs]")
        table.add_column("Avg F Cost [CUs]")
        table.add_column("Avg I Cost [CUs]")
        table.add_column("I/F [%]")

        for pss in org.permission_sets:
            total_refreshes = len(pss.permission_set.refreshes)

            i_f = (
                pss.inc_refreshes_cost_avg / pss.full_refreshes_cost_avg
                if pss.full_refreshes
                else 0
            )

            permission_set_text = await permission_set_human_description(
                pss.permission_set.permission_set_json
            )

            refresh_summary = ""

            for r in pss.full_refreshes:
                refresh_summary += f"{r.elastic_refresh_log.timestamp} {r.elastic_refresh_log.job_guid}\n"
                refresh_summary += "\tTriggered by:\n"
                for reason in r.trigger_reasons or []:
                    refresh_summary += f"\t\t{reason.event_name}\n"
                    if reason.event_name == "SKUS_UPDATED":
                        refresh_summary += f"\t\t{reason.event_params}\n"

            table.add_row(
                f"{pss.permission_set.uuid}\nviewers: {pss.permission_set.viewers}\ncontributors: {pss.permission_set.contributors}\nset:\n{permission_set_text}\n{refresh_summary}",
                f"{len(pss.all_refreshes)}/{total_refreshes} ({pss.refreshes_percent:.2f}%)"
                if total_refreshes > 0
                else "No refreshes",
                f"{len(pss.full_refreshes)} ({len(pss.full_refreshes) / len(pss.all_refreshes) * 100:.2f}%)"
                if pss.all_refreshes
                else "N/A",
                f"{pss.cost:_.2f} ({pss.cost * cost_per_cu:_.2f}€)",
                f"{pss.permission_set.costs.cost:.2f} ({pss.permission_set.costs.cost * cost_per_cu:_.2f}€)",
                f"{pss.full_refreshes_cost_avg:_.2f} ({pss.full_refreshes_cost_avg * cost_per_cu:_.2f}€)",
                f"{pss.inc_refreshes_cost_avg:_.2f} ({pss.inc_refreshes_cost_avg * cost_per_cu:_.2f}€)",
                f"{i_f * 100:.2f}%",
            )

        print(table)
        org_cost = sum(pss.cost for pss in org.permission_sets)
        org_cost_percent = (
            sum(pss.cost for pss in org.permission_sets) / total_cost * 100
        )
        print(
            f"Total org cost [CUs]: {org_cost:_.2f} ({org_cost_percent:.2f}%), Total org cost [€]: {org_cost * cost_per_cu:_.2f}"
        )


async def amain(use_cache: bool = False):
    cache.use_cache = use_cache

    permission_set_summaries = await permission_sets.download_permission_set_data(
        permission_sets.PermissionSetSummary
    )

    org_summaries = await orgs.summarize_by_orgs(permission_set_summaries)

    await prepare_report(org_summaries)


def main(use_cache: bool = False):
    import asyncio

    asyncio.run(amain(use_cache))


if __name__ == "__main__":
    fire.Fire(main)
