import re
import sys
from datetime import datetime
from enum import StrEnum
from typing import AsyncIterable

from pydantic import BaseModel, RootModel

from pbi_tools.connectors import dataset_manager
from pbi_tools.connectors.powerbi import execute_dax
from pbi_tools.settings import settings


class FabricMetricsOperationCost(BaseModel):
    capacity_id: str
    version_name: str
    shard_id: str
    permission_set_id: str
    operation_name: str
    cost: float


class FabricMetricsPermissionSetCosts(BaseModel):
    cost: float = 0
    cost_percent: float | None = None
    operations: list[FabricMetricsOperationCost] = []
    shards: set[str] = set()


class OperationName(StrEnum):
    DATASET_ON_DEMAND_REFRESH = "Dataset On-Demand Refresh"
    DATASET_SCHEDULED_REFRESH = "Dataset Scheduled Refresh"
    QUERY = "Query"


class FabricMetric(BaseModel):
    cus: float
    duration: float
    operations_count: int
    throttling_min: float
    shard_id: str
    operation_name: OperationName


class FabricMetrics(RootModel[list[FabricMetric]]):
    def __iter__(self):
        return iter(self.root)

    def on_demand_refreshes(self) -> "FabricMetrics":
        return FabricMetrics(
            root=[
                r
                for r in self.root
                if r.operation_name == OperationName.DATASET_ON_DEMAND_REFRESH
            ]
        )

    def for_shard_id(self, shard_id: str) -> "FabricMetrics":
        return FabricMetrics(root=[r for r in self if r.shard_id == shard_id])


async def get_operation_costs():
    capacities = await dataset_manager.get_capacities()

    for capacity in capacities:
        if capacity.state != "Active":
            continue
        sys.stderr.write(
            f"Getting operation costs for capacity {capacity.name} ({capacity.id})\n"
        )
        result = await query_fabric_capacity_metrics(capacity.id)

        for row in result:
            workspace_name = row["Items[WorkspaceName]"]
            operation_name = row["OperationNames[OperationName]"]
            cu = row["[Dynamic M1 CU Preview]"]
            shard_id = row["Items[WorkspaceId]"]

            if match := re.search(r"^(\w+_[\d\.]+)_([\w\d\-]+)", workspace_name):
                version_name = match.group(1)
                permission_set_id = match.group(2)
            else:
                sys.stderr.write(f"Could not parse workspace name {workspace_name}")
                continue

            yield FabricMetricsOperationCost(
                capacity_id=capacity.id,
                version_name=version_name,
                shard_id=shard_id,
                permission_set_id=permission_set_id,
                operation_name=operation_name,
                cost=cu,
            )


async def summarize_operation_costs_by_permission_set(
    operation_costs: AsyncIterable[FabricMetricsOperationCost],
):
    costs_per_permission_set: dict[str, FabricMetricsPermissionSetCosts] = {}

    total_cost = 0

    async for operation_cost in operation_costs:
        if operation_cost.permission_set_id not in costs_per_permission_set:
            costs_per_permission_set[operation_cost.permission_set_id] = (
                FabricMetricsPermissionSetCosts()
            )

        cpps = costs_per_permission_set[operation_cost.permission_set_id]

        cpps.cost += operation_cost.cost
        total_cost += operation_cost.cost
        cpps.operations.append(operation_cost)
        cpps.shards.add(operation_cost.shard_id)

    sorted_by_cost = sorted(
        costs_per_permission_set.items(), key=lambda x: x[1].cost, reverse=True
    )

    for _, cpps in sorted_by_cost:
        cpps.cost_percent = cpps.cost / total_cost * 100

    return sorted_by_cost


async def query_fabric_capacity_metrics(capacity_id: str):
    query = f"""
        DEFINE
            MPARAMETER 'CapacityID' =
                "{capacity_id}"


        EVALUATE
        SUMMARIZECOLUMNS(
            Items[WorkspaceName],
            Items[WorkspaceId],
            OperationNames[OperationName],
            "Dynamic M1 CU Preview", [Dynamic M1 CU Preview]
        )
        ORDER BY
            Items[WorkspaceName] ASC,
            OperationNames[OperationName] ASC
    """

    result = await execute_dax(
        workspace_id=settings().powerbi_capacity_metrics_workspace_id,
        dataset_id=settings().powerbi_capacity_metrics_dataset_id,
        dax_query=query,
    )

    return result


async def query_fabric_capacity_metrics_v2(capacity_id: str) -> FabricMetrics:
    query = f"""
        DEFINE
            MPARAMETER 'CapacityID' =
                "{capacity_id}"


        EVALUATE
        MetricsByItemandOperation
        order by [WorkspaceId]
    """

    result = await execute_dax(
        workspace_id=settings().powerbi_capacity_metrics_workspace_id,
        dataset_id=settings().powerbi_capacity_metrics_dataset_id,
        dax_query=query,
    )

    return FabricMetrics(
        root=[
            FabricMetric(
                cus=row["MetricsByItemandOperation[sum_CU]"],
                duration=row["MetricsByItemandOperation[sum_duration]"],
                operations_count=row["MetricsByItemandOperation[count_operations]"],
                throttling_min=row["MetricsByItemandOperation[Throttling (min)]"],
                operation_name=row["MetricsByItemandOperation[OperationName]"],
                shard_id=row["MetricsByItemandOperation[WorkspaceId]"],
            )
            for row in result
        ]
    )


async def query_item_id_workspace_id(capacity_id: str):
    query = f"""
        DEFINE
            MPARAMETER 'CapacityID' =
                "{capacity_id}"

        EVALUATE
            SUMMARIZECOLUMNS(
                Items[WorkspaceId],
                Items[ItemId]
            )
    """

    result = await execute_dax(
        workspace_id=settings().powerbi_capacity_metrics_workspace_id,
        dataset_id=settings().powerbi_capacity_metrics_dataset_id,
        dax_query=query,
    )

    item_id_to_workspace_id = {}

    for row in result:
        item_id_to_workspace_id[row["Items[ItemId]"]] = row["Items[WorkspaceId]"]

    return item_id_to_workspace_id


async def get_fabric_capacity_metrics_refreshes(capacity_id: str):
    result: FabricMetrics = await query_fabric_capacity_metrics(capacity_id)

    return result.on_demand_refreshes()
