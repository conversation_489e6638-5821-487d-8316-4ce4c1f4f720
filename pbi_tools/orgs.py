from pydantic import BaseModel

from pbi_tools import permission_sets, refresh_history
from pbi_tools.connectors import user_service


class OrgPermissionSetSummary(BaseModel):
    permission_set: permission_sets.PermissionSetSummary
    org: "OrganizationSummary"
    full_refreshes: list[refresh_history.Refresh] = []
    inc_refreshes: list[refresh_history.Refresh] = []
    all_refreshes: list[refresh_history.Refresh] = []
    refreshes_percent: float = 0
    cost: float = 0

    full_refreshes_cost_avg: float = 0
    inc_refreshes_cost_avg: float = 0
    all_refreshes_cost_avg: float = 0

    def calculate(self):
        self._assign_refreshes()

        self.refreshes_percent = (
            len(self.all_refreshes) / len(self.permission_set.refreshes) * 100
            if len(self.permission_set.refreshes) > 0
            else 0
        )
        self.cost = (
            self.permission_set.costs.cost
            * len(self.all_refreshes)
            / len(self.permission_set.refreshes)
            if len(self.permission_set.refreshes) > 0
            else 0
        )
        self.full_refreshes_cost_avg = (
            sum(r.fabric_metrics.cus for r in self.full_refreshes)
            / len(self.full_refreshes)
            if len(self.full_refreshes) > 0
            else 0
        )
        self.inc_refreshes_cost_avg = (
            sum(r.fabric_metrics.cus for r in self.inc_refreshes)
            / len(self.inc_refreshes)
            if len(self.inc_refreshes) > 0
            else 0
        )
        self.all_refreshes_cost_avg = (
            sum(r.fabric_metrics.cus for r in self.all_refreshes)
            / len(self.all_refreshes)
            if len(self.all_refreshes) > 0
            else 0
        )

    def _assign_refreshes(self):
        legacy_id = self.org.studio_id()
        org_id = self.org.org_id()

        for refresh in self.permission_set.refreshes:
            for reason in refresh.trigger_reasons or []:
                legacy_id_matches = (
                    legacy_id and reason.event_params.get("studio_id") == legacy_id
                )
                org_id_matches = (
                    org_id and reason.event_params.get("organization_id") == org_id
                )
                if legacy_id_matches or org_id_matches:
                    if refresh.elastic_refresh_log.is_full:
                        self.full_refreshes.append(refresh)
                    else:
                        self.inc_refreshes.append(refresh)
                    self.all_refreshes.append(refresh)

                    break


class OrganizationSummary(BaseModel):
    org: user_service.Organization | None = None
    user: user_service.User | None = None
    permission_sets: list[OrgPermissionSetSummary] = []

    def name(self):
        name = ""
        if self.org:
            name += f"{self.org.name} ({self.org.id} / {self.org.legacy_id})"
        if self.user:
            name += f" {self.user.first_name} {self.user.last_name} {self.user.company_name} ({self.user.id} / {self.user.legacy_id})"
        return name

    def org_id(self):
        if self.org is not None:
            return self.org.id
        if self.user is not None:
            return self.user.id
        return "None"

    def studio_id(self):
        if self.org is not None:
            return self.org.legacy_id
        if self.user is not None:
            return self.user.legacy_id
        return "None"


async def summarize_by_orgs(permission_set_summaries):
    orgs: dict[int, OrganizationSummary] = {}
    for permission_set in permission_set_summaries:
        for studio_id in permission_set.contributors:
            if studio_id not in orgs:
                orgs[studio_id] = OrganizationSummary()

            org = orgs[studio_id]
            org.permission_sets.append(
                OrgPermissionSetSummary(permission_set=permission_set, org=org)
            )

    for studio_id, org in orgs.items():
        if org.org is None:
            org.org = await user_service.get_org_by_studio_id(studio_id)

        if org.user is None:
            org.user = await user_service.get_user_by_studio_id(studio_id)

        for permission_set in org.permission_sets:
            permission_set.calculate()

    return orgs
