[tool.poetry]
name = "pbi-tools"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
rich = "^13.7.1"
fire = "^0.6.0"
httpx = "^0.27.0"
pydantic-settings = "^2.4.0"
pydantic = "^2.8.2"
azure-identity = "^1.17.1"
aiohttp = "^3.10.0"
pyodbc = "^5.1.0"
sqlalchemy = "^2.0.31"
tqdm = "^4.66.5"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
