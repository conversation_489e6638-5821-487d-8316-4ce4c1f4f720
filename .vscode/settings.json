{
  "[python]": {
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit",
      "source.sortImports": "explicit"
    },
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.formatOnSave": true,
    "editor.rulers": [88]
  },
  "editor.formatOnSave": true,
  "editor.trimAutoWhitespace": true,
  "files.exclude": {
    "**/.pytest_cache": true,
    "**/__pycache__": true,
    "**/.ruff_cache": true
  },
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "python.analysis.autoFormatStrings": true,
  "python.analysis.autoImportCompletions": true,
  "python.analysis.inlayHints.callArgumentNames": "partial",
  "python.analysis.inlayHints.functionReturnTypes": true,
  "python.analysis.inlayHints.pytestParameters": true,
  "python.analysis.inlayHints.variableTypes": true,
  "python.analysis.typeCheckingMode": "basic",
  "python.testing.pytestArgs": ["-vvv", "--random-order"],
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "ruff.lint.args": [
    // For better debugging expirience. After commenting out parts of code, vscode
    // will not remove the imports, which will be needed in case of uncommenting
    // the code. Ruff will remove unsued imports while running within a git hook
    "--unfixable=F401,F841"
  ],
  "workbench.colorCustomizations": {
    "activityBarBadge.foreground": "#ffffff",
    "commandCenter.border": "#c4e8c1",
    "sash.hoverBorder": "#7dd87a",
    "statusBar.background": "#e8f5e8",
    "statusBar.foreground": "#2d5a2d",
    "statusBarItem.hoverBackground": "#d4edd4",
    "statusBarItem.remoteBackground": "#c4e8c1",
    "statusBarItem.remoteForeground": "#2d5a2d",
    "titleBar.activeBackground": "#a8e6a3",
    "titleBar.activeForeground": "#2d5a2d",
    "titleBar.inactiveBackground": "#f0f8f0",
    "titleBar.inactiveForeground": "#5a8a5a"
  }
}
