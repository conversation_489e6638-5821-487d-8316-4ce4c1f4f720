## First time setup

1. Install aha
```bash
sudo apt install aha
```

2. Install project
```bash
poetry install
```

3. Fill out .env file with values for variables from settings.py (you need credentials from production env)

## Running

```bash
poetry run python -m pbi_tools | aha > reports/report.html
```

## Running with data from cache (speed up subsequent runs)

```bash
poetry run python -m pbi_tools --use-cache | aha > reports/report.html
```